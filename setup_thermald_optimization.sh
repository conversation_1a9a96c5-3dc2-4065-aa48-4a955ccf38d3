#!/bin/bash

# Thermald Optimization Setup for Dell Precision 5560
# Configures thermald with Dell-specific thermal management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration paths
THERMALD_CONFIG_DIR="/etc/thermald"
CUSTOM_CONFIG="$THERMALD_CONFIG_DIR/thermal-conf.xml"
BACKUP_DIR="/etc/thermald/backup_$(date +%Y%m%d_%H%M%S)"

echo -e "${BLUE}=== Thermald Optimization Setup ===${NC}"
echo "Dell Precision 5560 Thermal Configuration"
echo

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}This script must be run as root (use sudo)${NC}"
   exit 1
fi

# Check if thermald is installed
if ! command -v thermald >/dev/null 2>&1; then
    echo -e "${RED}Thermald is not installed. Installing...${NC}"
    apt update && apt install -y thermald
fi

# Create backup directory
echo -e "${YELLOW}Creating backup of current configuration...${NC}"
mkdir -p "$BACKUP_DIR"

# Backup existing configuration
if [ -f "$CUSTOM_CONFIG" ]; then
    cp "$CUSTOM_CONFIG" "$BACKUP_DIR/"
    echo "Backed up existing thermal-conf.xml"
fi

if [ -f "$THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml" ]; then
    cp "$THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml" "$BACKUP_DIR/"
    echo "Backed up existing thermal-cpu-cdev-order.xml"
fi

echo -e "${GREEN}Backup created at: $BACKUP_DIR${NC}"

# Function to show current thermald status
show_current_status() {
    echo -e "${BLUE}=== Current Thermald Status ===${NC}"
    systemctl status thermald --no-pager -l | head -10
    echo
    echo "Current thermal zones:"
    for zone in /sys/class/thermal/thermal_zone*; do
        if [ -r "$zone/type" ] && [ -r "$zone/temp" ]; then
            type=$(cat "$zone/type" 2>/dev/null)
            temp=$(cat "$zone/temp" 2>/dev/null)
            temp_c=$((temp / 1000))
            echo "  $(basename $zone): $type = ${temp_c}°C"
        fi
    done | head -10
    echo
}

# Function to install Dell-optimized configuration
install_dell_config() {
    echo -e "${YELLOW}Installing Dell Precision 5560 thermal configuration...${NC}"
    
    # Check if the config file exists in current directory
    if [ ! -f "thermald_dell_config.xml" ]; then
        echo -e "${RED}Error: thermald_dell_config.xml not found in current directory${NC}"
        echo "Please ensure the configuration file is present"
        exit 1
    fi
    
    # Install the configuration
    cp "thermald_dell_config.xml" "$CUSTOM_CONFIG"
    chown root:root "$CUSTOM_CONFIG"
    chmod 644 "$CUSTOM_CONFIG"
    
    echo -e "${GREEN}Dell thermal configuration installed${NC}"
}

# Function to optimize CPU cooling device order
optimize_cpu_cooling_order() {
    echo -e "${YELLOW}Optimizing CPU cooling device order...${NC}"
    
    cat > "$THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml" << 'EOF'
<!--
Dell Precision 5560 CPU Cooling Device Order
Optimized for performance workloads with thermal management
-->

<CoolingDeviceOrder>
    <!-- Order optimized for Dell Precision 5560 -->
    <CoolingDevice>rapl_controller</CoolingDevice>     <!-- Power limiting first -->
    <CoolingDevice>intel_pstate</CoolingDevice>        <!-- CPU frequency scaling -->
    <CoolingDevice>dell-smm-fan1</CoolingDevice>       <!-- Primary fan -->
    <CoolingDevice>dell-smm-fan2</CoolingDevice>       <!-- Secondary fan -->
    <CoolingDevice>intel_powerclamp</CoolingDevice>    <!-- CPU throttling -->
    <CoolingDevice>Processor</CoolingDevice>           <!-- Generic CPU control -->
</CoolingDeviceOrder>
EOF
    
    chown root:root "$THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml"
    chmod 644 "$THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml"
    
    echo -e "${GREEN}CPU cooling order optimized${NC}"
}

# Function to restart and verify thermald
restart_and_verify() {
    echo -e "${YELLOW}Restarting thermald service...${NC}"
    
    # Restart thermald
    systemctl restart thermald
    sleep 3
    
    # Verify it's running
    if systemctl is-active --quiet thermald; then
        echo -e "${GREEN}✅ Thermald restarted successfully${NC}"
    else
        echo -e "${RED}❌ Thermald failed to restart${NC}"
        echo "Checking logs..."
        journalctl -u thermald --since "1 minute ago" --no-pager
        return 1
    fi
    
    # Show new status
    echo
    echo -e "${BLUE}=== Updated Thermald Status ===${NC}"
    systemctl status thermald --no-pager -l | head -8
}

# Function to create monitoring script
create_monitoring_script() {
    echo -e "${YELLOW}Creating thermald monitoring script...${NC}"
    
    cat > "/usr/local/bin/thermald-monitor" << 'EOF'
#!/bin/bash
# Thermald monitoring script for Dell Precision 5560

echo "=== Thermald Status Monitor ==="
echo "Date: $(date)"
echo

echo "Service Status:"
systemctl is-active thermald && echo "✅ Active" || echo "❌ Inactive"
echo

echo "Current Temperatures:"
for zone in /sys/class/thermal/thermal_zone*; do
    if [ -r "$zone/type" ] && [ -r "$zone/temp" ]; then
        type=$(cat "$zone/type" 2>/dev/null)
        temp=$(cat "$zone/temp" 2>/dev/null)
        temp_c=$((temp / 1000))
        if [[ "$type" == *"pkg_temp"* ]] || [[ "$type" == *"TCPU"* ]]; then
            echo "  $type: ${temp_c}°C"
        fi
    fi
done

echo
echo "Fan Status:"
for fan in /sys/class/thermal/cooling_device*; do
    if [ -r "$fan/type" ]; then
        type=$(cat "$fan/type" 2>/dev/null)
        if [[ "$type" == *"dell-smm-fan"* ]]; then
            cur_state=$(cat "$fan/cur_state" 2>/dev/null || echo "unknown")
            max_state=$(cat "$fan/max_state" 2>/dev/null || echo "unknown")
            echo "  $type: $cur_state/$max_state"
        fi
    fi
done

echo
echo "Recent thermald logs:"
journalctl -u thermald --since "10 minutes ago" --no-pager | tail -5
EOF
    
    chmod +x "/usr/local/bin/thermald-monitor"
    echo -e "${GREEN}Monitoring script created: /usr/local/bin/thermald-monitor${NC}"
}

# Function to show usage instructions
show_usage_instructions() {
    echo
    echo -e "${BLUE}=== Thermald Optimization Complete ===${NC}"
    echo
    echo -e "${GREEN}✅ Dell Precision 5560 thermal configuration installed${NC}"
    echo -e "${GREEN}✅ CPU cooling device order optimized${NC}"
    echo -e "${GREEN}✅ Thermald service restarted${NC}"
    echo -e "${GREEN}✅ Monitoring tools configured${NC}"
    echo
    echo -e "${YELLOW}Usage Instructions:${NC}"
    echo "• Monitor thermald: sudo thermald-monitor"
    echo "• Check service: systemctl status thermald"
    echo "• View logs: journalctl -u thermald -f"
    echo "• Thermal zones: cat /sys/class/thermal/thermal_zone*/temp"
    echo
    echo -e "${YELLOW}Configuration Files:${NC}"
    echo "• Main config: $CUSTOM_CONFIG"
    echo "• CPU order: $THERMALD_CONFIG_DIR/thermal-cpu-cdev-order.xml"
    echo "• Backup: $BACKUP_DIR"
    echo
    echo -e "${YELLOW}Expected Behavior:${NC}"
    echo "• Power limiting starts at 75°C"
    echo "• CPU frequency scaling at 80°C"
    echo "• Fan ramping at 80°C"
    echo "• Emergency cooling at 85°C"
    echo
    echo -e "${YELLOW}To revert changes:${NC}"
    echo "sudo cp $BACKUP_DIR/* $THERMALD_CONFIG_DIR/"
    echo "sudo systemctl restart thermald"
}

# Main execution
echo -e "${BLUE}Starting thermald optimization...${NC}"
echo

# Show current status
show_current_status

# Ask for confirmation
echo -e "${YELLOW}This will install Dell-optimized thermald configuration.${NC}"
echo "Current configuration will be backed up."
echo
read -p "Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Perform optimization
install_dell_config
optimize_cpu_cooling_order
restart_and_verify
create_monitoring_script
show_usage_instructions

echo
echo -e "${GREEN}🎯 Thermald optimization completed successfully!${NC}"
echo -e "${BLUE}Monitor your temperatures and adjust thresholds if needed.${NC}"
