#!/bin/bash

# Phase 5: Desktop Effects Optimization - CORRECTED
# Proper Cinnamon desktop settings for performance optimization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== Phase 5: Desktop Effects Optimization (Corrected) ===${NC}"
echo "Optimizing Cinnamon desktop effects for performance"
echo

# Function to backup current settings
backup_settings() {
    echo -e "${YELLOW}Creating backup of current desktop settings...${NC}"
    
    BACKUP_FILE="$HOME/cinnamon_settings_backup_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$BACKUP_FILE" << EOF
# Cinnamon Desktop Settings Backup - $(date)
# To restore: source this file

# Animation settings
gsettings set org.cinnamon.desktop.interface enable-animations $(gsettings get org.cinnamon.desktop.interface enable-animations)

# Desktop effects
gsettings set org.cinnamon desktop-effects $(gsettings get org.cinnamon desktop-effects)
gsettings set org.cinnamon desktop-effects-close $(gsettings get org.cinnamon desktop-effects-close)
gsettings set org.cinnamon desktop-effects-minimize $(gsettings get org.cinnamon desktop-effects-minimize)
gsettings set org.cinnamon desktop-effects-map $(gsettings get org.cinnamon desktop-effects-map)
gsettings set org.cinnamon window-effect-speed $(gsettings get org.cinnamon window-effect-speed)

# Muffin compositor settings
gsettings set org.cinnamon.muffin unredirect-fullscreen-windows $(gsettings get org.cinnamon.muffin unredirect-fullscreen-windows)
EOF
    
    echo -e "${GREEN}Settings backed up to: $BACKUP_FILE${NC}"
}

# Function to show current settings
show_current_settings() {
    echo -e "${BLUE}=== Current Desktop Settings ===${NC}"
    echo "Animations enabled: $(gsettings get org.cinnamon.desktop.interface enable-animations)"
    echo "Desktop effects: $(gsettings get org.cinnamon desktop-effects)"
    echo "Window effect speed: $(gsettings get org.cinnamon window-effect-speed)"
    echo "Unredirect fullscreen: $(gsettings get org.cinnamon.muffin unredirect-fullscreen-windows)"
    echo "Close effect: $(gsettings get org.cinnamon desktop-effects-close)"
    echo "Minimize effect: $(gsettings get org.cinnamon desktop-effects-minimize)"
    echo
}

# Function to apply conservative optimization
apply_conservative_optimization() {
    echo -e "${YELLOW}Applying conservative desktop optimization...${NC}"
    
    # Keep animations but make them faster
    echo "• Keeping animations enabled but optimizing speed"
    gsettings set org.cinnamon window-effect-speed 1  # Faster animations (0=slow, 1=normal, 2=fast)
    
    # Ensure fullscreen unredirection is enabled (already true)
    echo "• Ensuring fullscreen unredirection is enabled"
    gsettings set org.cinnamon.muffin unredirect-fullscreen-windows true
    
    # Optimize specific effects for performance
    echo "• Optimizing window effects"
    gsettings set org.cinnamon desktop-effects-close 'traditional'  # Simple close effect
    gsettings set org.cinnamon desktop-effects-minimize 'traditional'  # Simple minimize effect
    gsettings set org.cinnamon desktop-effects-map 'traditional'  # Simple window opening
    
    echo -e "${GREEN}✅ Conservative optimization applied${NC}"
}

# Function to apply moderate optimization
apply_moderate_optimization() {
    echo -e "${YELLOW}Applying moderate desktop optimization...${NC}"
    
    # Disable animations for better performance
    echo "• Disabling desktop animations"
    gsettings set org.cinnamon.desktop.interface enable-animations false
    
    # Simplify effects
    echo "• Simplifying window effects"
    gsettings set org.cinnamon desktop-effects-close 'none'
    gsettings set org.cinnamon desktop-effects-minimize 'none'
    gsettings set org.cinnamon desktop-effects-map 'none'
    
    # Keep fullscreen unredirection
    gsettings set org.cinnamon.muffin unredirect-fullscreen-windows true
    
    echo -e "${GREEN}✅ Moderate optimization applied${NC}"
}

# Function to apply aggressive optimization
apply_aggressive_optimization() {
    echo -e "${YELLOW}Applying aggressive desktop optimization...${NC}"
    
    # Disable all animations
    echo "• Disabling all animations"
    gsettings set org.cinnamon.desktop.interface enable-animations false
    
    # Disable desktop effects entirely
    echo "• Disabling desktop effects"
    gsettings set org.cinnamon desktop-effects false
    
    # Disable all window effects
    echo "• Disabling all window effects"
    gsettings set org.cinnamon desktop-effects-close 'none'
    gsettings set org.cinnamon desktop-effects-minimize 'none'
    gsettings set org.cinnamon desktop-effects-map 'none'
    gsettings set org.cinnamon desktop-effects-workspace 'none'
    
    # Optimize compositor
    gsettings set org.cinnamon.muffin unredirect-fullscreen-windows true
    
    echo -e "${GREEN}✅ Aggressive optimization applied${NC}"
}

# Function to restore original settings
restore_original_settings() {
    echo -e "${YELLOW}Restoring original desktop settings...${NC}"
    
    # Restore to typical Cinnamon defaults
    gsettings set org.cinnamon.desktop.interface enable-animations true
    gsettings set org.cinnamon desktop-effects true
    gsettings set org.cinnamon desktop-effects-close 'traditional'
    gsettings set org.cinnamon desktop-effects-minimize 'traditional'
    gsettings set org.cinnamon desktop-effects-map 'traditional'
    gsettings set org.cinnamon window-effect-speed 1
    gsettings set org.cinnamon.muffin unredirect-fullscreen-windows true
    
    echo -e "${GREEN}✅ Original settings restored${NC}"
}

# Function to show optimization menu
show_optimization_menu() {
    echo -e "${BLUE}=== Desktop Optimization Options ===${NC}"
    echo "1. Conservative (Keep visual quality, optimize speed)"
    echo "2. Moderate (Reduce animations, keep some effects)"
    echo "3. Aggressive (Disable all effects for maximum performance)"
    echo "4. Restore original settings"
    echo "5. Show current settings"
    echo "6. Exit"
    echo
}

# Function to test current performance
test_current_performance() {
    echo -e "${BLUE}=== Current Desktop Performance Test ===${NC}"
    echo "Desktop memory usage:"
    ps aux | grep -E "(cinnamon|muffin)" | grep -v grep | awk '{print $11 ": " $6/1024 " MB"}'
    echo
    echo "Graphics info:"
    glxinfo | grep -E "(OpenGL renderer|direct rendering)" 2>/dev/null || echo "glxinfo not available"
    echo
}

# Main execution
echo "This script will optimize Cinnamon desktop effects for better performance."
echo "Your current thermal situation is good, so this is optional optimization."
echo

# Show current settings
show_current_settings

# Create backup
backup_settings

# Test current performance
test_current_performance

# Show menu and get user choice
while true; do
    show_optimization_menu
    read -p "Select option (1-6): " choice
    
    case $choice in
        1)
            apply_conservative_optimization
            break
            ;;
        2)
            apply_moderate_optimization
            break
            ;;
        3)
            apply_aggressive_optimization
            break
            ;;
        4)
            restore_original_settings
            break
            ;;
        5)
            show_current_settings
            continue
            ;;
        6)
            echo "Exiting without changes."
            exit 0
            ;;
        *)
            echo "Invalid option. Please select 1-6."
            continue
            ;;
    esac
done

echo
echo -e "${BLUE}=== Updated Desktop Settings ===${NC}"
show_current_settings

echo -e "${GREEN}=== Desktop Optimization Complete ===${NC}"
echo
echo -e "${YELLOW}Notes:${NC}"
echo "• Changes take effect immediately"
echo "• You can run this script again to try different optimization levels"
echo "• Settings backup available for restoration"
echo "• Monitor thermal impact with: ./thermal_monitor_enhanced.sh"
echo
echo -e "${YELLOW}To manually restore settings:${NC}"
echo "gsettings set org.cinnamon.desktop.interface enable-animations true"
echo "gsettings set org.cinnamon desktop-effects true"
echo
echo -e "${BLUE}Desktop optimization completed!${NC}"
