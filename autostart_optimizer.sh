#!/bin/bash

# Phase 2: Autostart Optimization Script
# This script implements the requested autostart changes

set -e

AUTOSTART_DIR="$HOME/.config/autostart"
BACKUP_DIR="$HOME/.config/autostart_backup_$(date +%Y%m%d_%H%M%S)"

echo "=== AUTOSTART OPTIMIZATION - PHASE 2 ==="
echo "Creating backup at: $BACKUP_DIR"

# Create backup
mkdir -p "$BACKUP_DIR"
cp -r "$AUTOSTART_DIR"/* "$BACKUP_DIR"/ 2>/dev/null || true

echo "Backup created successfully"

# 1. Modify Downloads Folder Sync to check internet connectivity
echo "Modifying Downloads Folder Sync for internet connectivity check..."
cat > "$AUTOSTART_DIR/Downloads Folder Sync.desktop" << 'EOF'
[Desktop Entry]
Type=Application
Exec=bash -c 'while true; do if ping -c 1 ******* >/dev/null 2>&1; then inotify-hookable -w ~/Downloads -r -c "rclone bisync -v --copy-links --compare size,modtime --check-access --check-filename .RCLONE_TEST --remove-empty-dirs --recover CompressedDownloads: /home/<USER>/Downloads"; break; else sleep 30; fi; done'
X-GNOME-Autostart-enabled=true
NoDisplay=false
Hidden=false
Name[en_ZA]=Downloads Folder Sync (Internet-aware)
Comment[en_ZA]=This keeps the Download folder synchronised with a Minio S3 bucket when internet is available
X-GNOME-Autostart-Delay=10
EOF

# 2. Disable applications by setting Hidden=true (keeps files but disables them)
echo "Disabling non-essential autostart applications..."

# Disable Gearlever (Flatpak updater - can run manually)
if [ -f "$AUTOSTART_DIR/it.mijorus.gearlever.desktop" ]; then
    echo "Hidden=true" >> "$AUTOSTART_DIR/it.mijorus.gearlever.desktop"
    echo "Disabled: Gearlever (Flatpak updater)"
fi

# Disable Print Applet (only needed if printing regularly)
if [ -f "$AUTOSTART_DIR/print-applet.desktop" ]; then
    echo "Hidden=true" >> "$AUTOSTART_DIR/print-applet.desktop"
    echo "Disabled: Print Queue Applet"
fi

# Disable Remmina Applet (only needed if using remote desktop)
if [ -f "$AUTOSTART_DIR/remmina-applet.desktop" ]; then
    echo "Hidden=true" >> "$AUTOSTART_DIR/remmina-applet.desktop"
    echo "Disabled: Remmina Applet"
fi

# Disable Nvidia Prime (only shows tray icon)
if [ -f "$AUTOSTART_DIR/nvidia-prime.desktop" ]; then
    echo "Hidden=true" >> "$AUTOSTART_DIR/nvidia-prime.desktop"
    echo "Disabled: Nvidia Prime Applet"
fi

# Disable Onedriver (can be started manually when needed)
if [ -f "$AUTOSTART_DIR/onedriver.desktop" ]; then
    echo "Hidden=true" >> "$AUTOSTART_DIR/onedriver.desktop"
    echo "Disabled: Onedriver"
fi

echo ""
echo "=== AUTOSTART OPTIMIZATION COMPLETE ==="
echo ""
echo "KEPT ENABLED:"
echo "✅ 1Password - Password manager"
echo "✅ Ulauncher - Application launcher"
echo "✅ Safe Eyes - Eye strain protection"
echo "✅ Mint Update Manager - System updates"
echo "✅ Downloads Folder Sync - Now internet-aware"
echo ""
echo "DISABLED (can be started manually):"
echo "❌ Gearlever - Flatpak updater"
echo "❌ Print Queue Applet - Printing support"
echo "❌ Remmina Applet - Remote desktop"
echo "❌ Nvidia Prime Applet - GPU switching"
echo "❌ Onedriver - OneDrive sync"
echo ""
echo "To re-enable any application, edit the .desktop file in:"
echo "$AUTOSTART_DIR"
echo "and remove the 'Hidden=true' line"
echo ""
echo "Backup location: $BACKUP_DIR"
echo ""
echo "Changes will take effect after next login/reboot"
