[Desktop Entry]
Version=1.0
Name=Creality Print (GPU Accelerated)
GenericName=3D Printing Slicer with GPU
Comment=3D printing slicer software with NVIDIA GPU acceleration
Keywords=3d;printing;slicer;creality;gpu;nvidia;
Exec=env __NV_PRIME_RENDER_OFFLOAD=1 __GLX_VENDOR_LIBRARY_NAME=nvidia /home/<USER>/.local/share/AppImages/creality_print.appimage %F
Icon=creality-print
Terminal=false
Type=Application
Categories=Graphics;3DGraphics;Engineering;
StartupNotify=true
PrefersNonDefaultGPU=true
X-KDE-RunOnDiscreteGpu=true
MimeType=application/x-3mf;model/stl;
