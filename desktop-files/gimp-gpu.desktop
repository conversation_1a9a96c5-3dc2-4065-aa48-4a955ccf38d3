[Desktop Entry]
Version=1.0
Name=GIMP (GPU Accelerated)
GenericName=Image Editor with GPU
Comment=Create images and edit photographs with NVIDIA GPU acceleration
Keywords=GIMP;graphic;design;illustration;painting;gpu;nvidia;opencl;
Exec=env __NV_PRIME_RENDER_OFFLOAD=1 __GLX_VENDOR_LIBRARY_NAME=nvidia flatpak run org.gimp.GIMP %U
Icon=org.gimp.GIMP
StartupNotify=true
NoDisplay=false
MimeType=image/bmp;image/g3fax;image/gif;image/x-fits;image/x-pcx;image/x-portable-anymap;image/x-portable-bitmap;image/x-portable-graymap;image/x-portable-pixmap;image/x-psd;image/x-sgi;image/x-tga;image/x-xbitmap;image/x-xwindowdump;image/x-xcf;image/x-compressed-xcf;image/x-gimp-gbr;image/x-gimp-pat;image/x-gimp-gih;image/x-sun-raster;image/tiff;image/jpeg;image/x-psp;application/postscript;image/png;image/x-icon;image/x-xpixmap;image/x-exr;image/x-webp;image/heif;image/avif;image/jxl;
Categories=Graphics;2DGraphics;RasterGraphics;GTK;
Type=Application
PrefersNonDefaultGPU=true
X-KDE-RunOnDiscreteGpu=true
