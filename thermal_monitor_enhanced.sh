#!/bin/bash

# Enhanced Thermal Monitoring Script
# Provides real-time thermal monitoring with alerts and logging

set -e

# Configuration
LOG_FILE="$HOME/thermal_monitor.log"
ALERT_TEMP=85  # Alert threshold in Celsius
CRITICAL_TEMP=95  # Critical threshold in Celsius
REFRESH_INTERVAL=2  # Seconds between updates
ENABLE_LOGGING=true
ENABLE_ALERTS=true

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get timestamp
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# Function to log thermal data
log_thermal_data() {
    if [ "$ENABLE_LOGGING" = true ]; then
        echo "$(get_timestamp) - $1" >> "$LOG_FILE"
    fi
}

# Function to send desktop notification
send_alert() {
    local message="$1"
    local urgency="$2"
    
    if [ "$ENABLE_ALERTS" = true ]; then
        notify-send -u "$urgency" "Thermal Alert" "$message" 2>/dev/null || true
        echo -e "${RED}ALERT: $message${NC}"
        log_thermal_data "ALERT: $message"
    fi
}

# Function to get CPU usage by top processes
get_top_cpu_processes() {
    ps aux --sort=-%cpu | head -6 | tail -5 | awk '{printf "  %-20s %5s%%\n", $11, $3}'
}

# Function to get thermal data
get_thermal_data() {
    local thermal_output
    thermal_output=$(sensors 2>/dev/null | grep -E "(Package|Core)" || echo "Thermal data unavailable")
    echo "$thermal_output"
}

# Function to extract package temperature
get_package_temp() {
    sensors 2>/dev/null | grep "Package id 0" | awk '{print $4}' | sed 's/+//;s/°C//' || echo "0"
}

# Function to get system load
get_system_load() {
    cat /proc/loadavg | awk '{print $1, $2, $3}'
}

# Function to get memory usage
get_memory_usage() {
    free -h | grep "Mem:" | awk '{printf "Used: %s/%s (%.1f%%)", $3, $2, ($3/$2)*100}' 2>/dev/null || echo "Memory data unavailable"
}

# Function to display thermal dashboard
display_thermal_dashboard() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                    THERMAL MONITORING DASHBOARD              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${GREEN}Timestamp: $(get_timestamp)${NC}"
    echo -e "${GREEN}Refresh: ${REFRESH_INTERVAL}s | Alert: ${ALERT_TEMP}°C | Critical: ${CRITICAL_TEMP}°C${NC}"
    echo
    
    # Get current package temperature for alerts
    local package_temp
    package_temp=$(get_package_temp)
    
    # Thermal data
    echo -e "${YELLOW}🌡️  THERMAL STATUS:${NC}"
    local thermal_data
    thermal_data=$(get_thermal_data)
    
    # Color code temperatures
    echo "$thermal_data" | while IFS= read -r line; do
        if echo "$line" | grep -q "Package"; then
            local temp=$(echo "$line" | awk '{print $4}' | sed 's/+//;s/°C//')
            if (( $(echo "$temp > $CRITICAL_TEMP" | bc -l 2>/dev/null || echo 0) )); then
                echo -e "${RED}$line${NC}"
            elif (( $(echo "$temp > $ALERT_TEMP" | bc -l 2>/dev/null || echo 0) )); then
                echo -e "${YELLOW}$line${NC}"
            else
                echo -e "${GREEN}$line${NC}"
            fi
        else
            echo "$line"
        fi
    done
    
    echo
    echo -e "${YELLOW}📊 SYSTEM LOAD:${NC}"
    echo "  Load Average: $(get_system_load)"
    echo
    
    echo -e "${YELLOW}💾 MEMORY USAGE:${NC}"
    echo "  $(get_memory_usage)"
    echo
    
    echo -e "${YELLOW}🔥 TOP CPU PROCESSES:${NC}"
    get_top_cpu_processes
    echo
    
    # Check for alerts
    if (( $(echo "$package_temp > $CRITICAL_TEMP" | bc -l 2>/dev/null || echo 0) )); then
        send_alert "CRITICAL: Package temperature ${package_temp}°C exceeds ${CRITICAL_TEMP}°C!" "critical"
    elif (( $(echo "$package_temp > $ALERT_TEMP" | bc -l 2>/dev/null || echo 0) )); then
        send_alert "WARNING: Package temperature ${package_temp}°C exceeds ${ALERT_TEMP}°C" "normal"
    fi
    
    # Log current status
    log_thermal_data "Package: ${package_temp}°C, Load: $(get_system_load | awk '{print $1}')"
    
    echo -e "${BLUE}Press Ctrl+C to exit | Log: $LOG_FILE${NC}"
}

# Function to show usage
show_usage() {
    echo "Enhanced Thermal Monitor"
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  -i, --interval SECONDS    Set refresh interval (default: 2)"
    echo "  -a, --alert-temp TEMP     Set alert temperature (default: 85)"
    echo "  -c, --critical-temp TEMP  Set critical temperature (default: 95)"
    echo "  -l, --log FILE           Set log file (default: ~/thermal_monitor.log)"
    echo "  --no-alerts              Disable desktop notifications"
    echo "  --no-logging             Disable logging"
    echo "  -h, --help               Show this help"
    echo
    echo "Examples:"
    echo "  $0                       # Start with default settings"
    echo "  $0 -i 5 -a 80           # 5-second refresh, 80°C alert threshold"
    echo "  $0 --no-alerts          # Disable notifications"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -i|--interval)
            REFRESH_INTERVAL="$2"
            shift 2
            ;;
        -a|--alert-temp)
            ALERT_TEMP="$2"
            shift 2
            ;;
        -c|--critical-temp)
            CRITICAL_TEMP="$2"
            shift 2
            ;;
        -l|--log)
            LOG_FILE="$2"
            shift 2
            ;;
        --no-alerts)
            ENABLE_ALERTS=false
            shift
            ;;
        --no-logging)
            ENABLE_LOGGING=false
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate inputs
if ! [[ "$REFRESH_INTERVAL" =~ ^[0-9]+$ ]] || [ "$REFRESH_INTERVAL" -lt 1 ]; then
    echo "Error: Refresh interval must be a positive integer"
    exit 1
fi

if ! [[ "$ALERT_TEMP" =~ ^[0-9]+$ ]] || [ "$ALERT_TEMP" -lt 1 ]; then
    echo "Error: Alert temperature must be a positive integer"
    exit 1
fi

if ! [[ "$CRITICAL_TEMP" =~ ^[0-9]+$ ]] || [ "$CRITICAL_TEMP" -lt 1 ]; then
    echo "Error: Critical temperature must be a positive integer"
    exit 1
fi

# Check dependencies
if ! command -v sensors >/dev/null 2>&1; then
    echo "Error: 'sensors' command not found. Please install lm-sensors:"
    echo "sudo apt install lm-sensors"
    exit 1
fi

if ! command -v bc >/dev/null 2>&1; then
    echo "Installing bc for temperature calculations..."
    sudo apt update && sudo apt install -y bc
fi

# Initialize log file
if [ "$ENABLE_LOGGING" = true ]; then
    echo "$(get_timestamp) - Thermal monitoring started" > "$LOG_FILE"
    echo "Log file: $LOG_FILE"
fi

# Trap Ctrl+C for clean exit
trap 'echo -e "\n${GREEN}Thermal monitoring stopped.${NC}"; log_thermal_data "Monitoring stopped"; exit 0' INT

# Main monitoring loop
echo "Starting thermal monitoring..."
echo "Alert threshold: ${ALERT_TEMP}°C"
echo "Critical threshold: ${CRITICAL_TEMP}°C"
echo "Refresh interval: ${REFRESH_INTERVAL}s"
echo

while true; do
    display_thermal_dashboard
    sleep "$REFRESH_INTERVAL"
done
