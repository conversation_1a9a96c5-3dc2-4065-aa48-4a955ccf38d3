#!/bin/bash

# FreeCAD Daily Tessellation Fix Script
# Addresses tessellation issues in development builds

echo "=== FreeCAD Daily Tessellation Fix ==="
echo

# Backup current config
echo "Creating backup..."
cp ~/.config/FreeCAD/user.cfg ~/.config/FreeCAD/user.cfg.backup-tessellation-$(date +%Y%m%d-%H%M%S)

echo "Current tessellation settings:"
grep -E "(MeshDeviation|MeshAngular|meshdeflection)" ~/.config/FreeCAD/user.cfg

echo
echo "=== Applying Ultra-High Quality Tessellation Settings ==="

# Ultra-fine tessellation for FreeCAD Daily
sed -i 's/MeshDeviation" Value="[^"]*"/MeshDeviation" Value="0.0005000000000"/' ~/.config/FreeCAD/user.cfg
sed -i 's/MeshAngularDeflection" Value="[^"]*"/MeshAngularDeflection" Value="0.5000000000000"/' ~/.config/FreeCAD/user.cfg
sed -i 's/meshdeflection" Value="[^"]*"/meshdeflection" Value="0.0005000000000"/' ~/.config/FreeCAD/user.cfg
sed -i 's/MaxDeviationExport" Value="[^"]*"/MaxDeviationExport" Value="0.0005000000000"/' ~/.config/FreeCAD/user.cfg

echo "New tessellation settings:"
grep -E "(MeshDeviation|MeshAngular|meshdeflection)" ~/.config/FreeCAD/user.cfg

echo
echo "=== Settings Applied ==="
echo "• MeshDeviation: 0.0005 (ultra-fine tessellation)"
echo "• MeshAngularDeflection: 0.5° (ultra-smooth angles)"
echo "• meshdeflection: 0.0005 (additional tessellation control)"
echo "• MaxDeviationExport: 0.0005 (export quality)"
echo
echo "=== Next Steps ==="
echo "1. Restart FreeCAD Daily"
echo "2. Test curved shapes - they should be very smooth"
echo "3. If still not smooth enough, consider installing stable FreeCAD:"
echo "   sudo apt install freecad"
echo
echo "=== Performance Note ==="
echo "These ultra-fine settings will use more GPU memory and processing."
echo "Your NVIDIA T1200 can handle it, but complex models may render slower."
echo
echo "=== Rollback if Needed ==="
echo "If performance is too slow, restore from backup:"
echo "cp ~/.config/FreeCAD/user.cfg.backup-tessellation-* ~/.config/FreeCAD/user.cfg"
