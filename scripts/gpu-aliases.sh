#!/bin/bash

# GPU Acceleration Aliases and Functions
# Source this file in your ~/.bashrc or ~/.zshrc

# NVIDIA GPU environment variables
export __NV_PRIME_RENDER_OFFLOAD=1
export __GLX_VENDOR_LIBRARY_NAME=nvidia

# Function for running applications with NVIDIA GPU
gpu-run() {
    __NV_PRIME_RENDER_OFFLOAD=1 __GLX_VENDOR_LIBRARY_NAME=nvidia "$@"
}

# Alias for compatibility
alias prime-run='gpu-run'

# Application-specific GPU aliases
alias blender-gpu='gpu-run blender'
alias freecad-gpu='gpu-run freecad'
alias freecad-daily-gpu='gpu-run freecad-daily'
alias gimp-gpu='gpu-run flatpak run org.gimp.GIMP'
alias darktable-gpu='gpu-run darktable'

# AppImage GPU aliases
alias cad-assistant-gpu='gpu-run ~/.local/share/AppImages/cad_assistant.appimage'
alias astocad-gpu='gpu-run ~/.local/share/AppImages/astocad.appimage'
alias creality-print-gpu='gpu-run ~/.local/share/AppImages/creality_print.appimage'

# GPU monitoring functions
gpu-status() {
    echo "=== GPU Status ==="
    nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,memory.used,memory.total --format=csv,noheader
    echo
    echo "=== PRIME Mode ==="
    prime-select query
    echo
    echo "=== Current OpenGL Renderer ==="
    glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs
}

gpu-processes() {
    echo "=== GPU Processes ==="
    nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv
}

gpu-monitor() {
    echo "Starting GPU monitor (Ctrl+C to stop)..."
    watch -n 2 'nvidia-smi --query-gpu=timestamp,name,temperature.gpu,utilization.gpu,memory.used,memory.total,power.draw --format=csv,noheader'
}

# Test GPU functionality
gpu-test() {
    echo "=== Testing GPU Switching ==="
    echo "Intel GPU (default):"
    glxinfo | grep -E "(OpenGL vendor|OpenGL renderer)" | head -2
    echo
    echo "NVIDIA GPU (PRIME offload):"
    gpu-run glxinfo | grep -E "(OpenGL vendor|OpenGL renderer)" | head -2
    echo
    echo "=== GPU Test Complete ==="
}

# OpenCL information
gpu-opencl() {
    echo "=== OpenCL Devices ==="
    if command -v clinfo >/dev/null 2>&1; then
        clinfo | grep -E "(Platform|Device Name|Device Type)"
    else
        echo "clinfo not installed. Install with: sudo apt install clinfo"
    fi
}

echo "GPU aliases and functions loaded!"
echo "Available commands:"
echo "  gpu-run <command>     - Run command with NVIDIA GPU"
echo "  blender-gpu          - Launch Blender with GPU"
echo "  freecad-gpu          - Launch FreeCAD with GPU"
echo "  freecad-daily-gpu    - Launch FreeCAD Daily with GPU"
echo "  gimp-gpu             - Launch GIMP with GPU"
echo "  darktable-gpu        - Launch Darktable with GPU"
echo "  gpu-status           - Show GPU status"
echo "  gpu-processes        - Show GPU processes"
echo "  gpu-monitor          - Monitor GPU in real-time"
echo "  gpu-test             - Test GPU switching"
echo "  gpu-opencl           - Show OpenCL devices"
