#!/bin/bash

# GPU Monitoring Script for NVIDIA T1200 Laptop GPU
# Provides real-time GPU utilization, memory usage, and application tracking

set -e

# Configuration
REFRESH_INTERVAL=2
LOG_FILE="$HOME/gpu_monitor.log"
ENABLE_LOGGING=true
ENABLE_ALERTS=true

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to get timestamp
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# Function to log GPU data
log_gpu_data() {
    if [ "$ENABLE_LOGGING" = true ]; then
        echo "$(get_timestamp) - $1" >> "$LOG_FILE"
    fi
}

# Function to get GPU utilization
get_gpu_utilization() {
    nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits 2>/dev/null || echo "0"
}

# Function to get GPU memory usage
get_gpu_memory() {
    nvidia-smi --query-gpu=memory.used,memory.total --format=csv,noheader,nounits 2>/dev/null || echo "0, 0"
}

# Function to get GPU temperature
get_gpu_temperature() {
    nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits 2>/dev/null || echo "0"
}

# Function to get GPU power usage
get_gpu_power() {
    nvidia-smi --query-gpu=power.draw,power.limit --format=csv,noheader,nounits 2>/dev/null || echo "0, 0"
}

# Function to get GPU processes
get_gpu_processes() {
    nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader 2>/dev/null || echo "No processes"
}

# Function to check PRIME status
get_prime_status() {
    prime-select query 2>/dev/null || echo "unknown"
}

# Function to get OpenGL renderer
get_opengl_renderer() {
    glxinfo | grep "OpenGL renderer" | cut -d: -f2 | xargs 2>/dev/null || echo "Unknown"
}

# Function to send desktop notification
send_alert() {
    local message="$1"
    local urgency="$2"
    
    if [ "$ENABLE_ALERTS" = true ]; then
        notify-send -u "$urgency" "GPU Alert" "$message" 2>/dev/null || true
        echo -e "${RED}ALERT: $message${NC}"
        log_gpu_data "ALERT: $message"
    fi
}

# Function to display GPU status
display_gpu_status() {
    clear
    echo -e "${CYAN}=== NVIDIA T1200 GPU Monitor ===${NC}"
    echo "$(get_timestamp)"
    echo "========================================"
    echo
    
    # GPU Utilization
    local gpu_util=$(get_gpu_utilization)
    echo -e "${YELLOW}🎮 GPU UTILIZATION:${NC}"
    echo "  GPU Usage: ${gpu_util}%"
    
    if (( gpu_util > 80 )); then
        echo -e "  ${RED}🔥 High GPU load${NC}"
    elif (( gpu_util > 50 )); then
        echo -e "  ${YELLOW}⚠️  Moderate GPU load${NC}"
    elif (( gpu_util > 0 )); then
        echo -e "  ${GREEN}✅ GPU active${NC}"
    else
        echo -e "  ${BLUE}💤 GPU idle${NC}"
    fi
    echo
    
    # GPU Memory
    local memory_info=$(get_gpu_memory)
    local memory_used=$(echo "$memory_info" | cut -d',' -f1 | xargs)
    local memory_total=$(echo "$memory_info" | cut -d',' -f2 | xargs)
    local memory_percent=0
    
    if [ "$memory_total" -gt 0 ]; then
        memory_percent=$((memory_used * 100 / memory_total))
    fi
    
    echo -e "${YELLOW}💾 GPU MEMORY:${NC}"
    echo "  Used: ${memory_used}MB / ${memory_total}MB (${memory_percent}%)"
    echo
    
    # GPU Temperature
    local gpu_temp=$(get_gpu_temperature)
    echo -e "${YELLOW}🌡️  GPU TEMPERATURE:${NC}"
    echo "  Temperature: ${gpu_temp}°C"
    
    if (( gpu_temp > 80 )); then
        echo -e "  ${RED}🔥 High temperature${NC}"
        send_alert "GPU temperature ${gpu_temp}°C is high" "normal"
    elif (( gpu_temp > 70 )); then
        echo -e "  ${YELLOW}⚠️  Elevated temperature${NC}"
    else
        echo -e "  ${GREEN}✅ Temperature normal${NC}"
    fi
    echo
    
    # GPU Power
    local power_info=$(get_gpu_power)
    local power_draw=$(echo "$power_info" | cut -d',' -f1 | xargs)
    local power_limit=$(echo "$power_info" | cut -d',' -f2 | xargs)
    
    echo -e "${YELLOW}⚡ GPU POWER:${NC}"
    echo "  Power Draw: ${power_draw}W / ${power_limit}W"
    echo
    
    # PRIME Status
    local prime_status=$(get_prime_status)
    echo -e "${YELLOW}🔄 PRIME STATUS:${NC}"
    echo "  Mode: $prime_status"
    echo
    
    # Current OpenGL Renderer
    echo -e "${YELLOW}🖥️  CURRENT RENDERER:${NC}"
    echo "  $(get_opengl_renderer)"
    echo
    
    # GPU Processes
    echo -e "${YELLOW}🔍 GPU PROCESSES:${NC}"
    local processes=$(get_gpu_processes)
    if [ "$processes" = "No processes" ]; then
        echo "  No GPU processes running"
    else
        echo "$processes" | while IFS=, read -r pid name memory; do
            echo "  PID: $pid | $name | Memory: ${memory}MB"
        done
    fi
    echo
    
    echo -e "${BLUE}Press Ctrl+C to stop monitoring${NC}"
    
    # Log current status
    log_gpu_data "GPU: ${gpu_util}% | Memory: ${memory_used}MB/${memory_total}MB | Temp: ${gpu_temp}°C | Power: ${power_draw}W"
}

# Main monitoring loop
echo -e "${GREEN}Starting GPU monitoring...${NC}"
echo "Logging to: $LOG_FILE"
echo

trap 'echo -e "\n${GREEN}GPU monitoring stopped.${NC}"; exit 0' INT

while true; do
    display_gpu_status
    sleep $REFRESH_INTERVAL
done
