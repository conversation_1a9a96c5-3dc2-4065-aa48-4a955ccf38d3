#!/usr/bin/env python3
"""
FreeCAD Smooth Curves Configuration Script
Optimizes tessellation and rendering settings for smooth curved shapes
"""

import sys
import os

def configure_freecad_smooth_curves():
    """Configure FreeCAD for smooth curved shapes"""
    
    # FreeCAD configuration script to run inside FreeCAD
    freecad_config = '''
import FreeCAD
import FreeCADGui

print("=== Configuring FreeCAD for Smooth Curves ===")

# Get parameter groups
view_prefs = FreeCAD.ParamGet("User parameter:BaseApp/Preferences/View")
display_prefs = FreeCAD.ParamGet("User parameter:BaseApp/Preferences/Display")

# === TESSELLATION SETTINGS (Most Important) ===
print("Configuring tessellation settings...")

# Mesh deviation - Lower values = smoother curves (default: 0.2)
view_prefs.SetFloat("MeshDeviation", 0.01)  # Much finer tessellation
print("  Mesh deviation: 0.01 (was likely 0.2)")

# Angular deflection - Lower values = smoother curves (default: 28.5)
view_prefs.SetFloat("MeshAngularDeflection", 5.0)  # Much smoother
print("  Angular deflection: 5.0 degrees (was likely 28.5)")

# Maximum deviation for high-quality rendering
view_prefs.SetFloat("MaximumDeviation", 0.005)  # Very fine
print("  Maximum deviation: 0.005")

# Maximum angular deflection for high-quality
view_prefs.SetFloat("MaximumAngularDeflection", 2.0)  # Very smooth
print("  Maximum angular deflection: 2.0 degrees")

# === ANTI-ALIASING AND SMOOTHING ===
print("Configuring anti-aliasing and smoothing...")

# Enable anti-aliasing
view_prefs.SetBool("AntiAliasing", True)
print("  Anti-aliasing: Enabled")

# Enable smooth lines
view_prefs.SetBool("SmoothLines", True)
print("  Smooth lines: Enabled")

# Enable transparency anti-aliasing
view_prefs.SetBool("TransparencyAntiAliasing", True)
print("  Transparency anti-aliasing: Enabled")

# === OPENGL SETTINGS ===
print("Configuring OpenGL settings...")

# Use VBO (Vertex Buffer Objects) for better performance
view_prefs.SetBool("UseVBO", True)
print("  VBO (Vertex Buffer Objects): Enabled")

# Enable hardware acceleration
view_prefs.SetBool("UseOpenGL", True)
print("  OpenGL acceleration: Enabled")

# === PART DESIGN SPECIFIC SETTINGS ===
print("Configuring Part Design settings...")

# Part Design preferences
part_prefs = FreeCAD.ParamGet("User parameter:BaseApp/Preferences/Mod/PartDesign")
part_prefs.SetFloat("MeshDeviation", 0.01)
part_prefs.SetFloat("MeshAngularDeflection", 5.0)
print("  Part Design tessellation: Optimized")

# === DISPLAY QUALITY ===
print("Configuring display quality...")

# Enable high-quality rendering
display_prefs.SetBool("HighQuality", True)
print("  High-quality rendering: Enabled")

# Set render cache mode for better performance
view_prefs.SetInt("RenderCache", 2)  # 0=Off, 1=On, 2=Auto
print("  Render cache: Auto")

# === VERIFICATION ===
print("\\n=== Current Settings Verification ===")
print(f"Mesh deviation: {view_prefs.GetFloat('MeshDeviation', 0.2)}")
print(f"Angular deflection: {view_prefs.GetFloat('MeshAngularDeflection', 28.5)}")
print(f"Anti-aliasing: {view_prefs.GetBool('AntiAliasing', False)}")
print(f"Smooth lines: {view_prefs.GetBool('SmoothLines', False)}")
print(f"VBO enabled: {view_prefs.GetBool('UseVBO', False)}")

print("\\n=== Configuration Complete ===")
print("Restart FreeCAD for all changes to take effect.")
print("For immediate effect in current session:")
print("1. Go to Edit → Preferences → Display → 3D View")
print("2. Verify the tessellation settings")
print("3. Close and reopen any documents")

# Create a test document to verify settings
doc = FreeCAD.newDocument("SmoothCurveTest")
print("\\nCreated test document. Create some curved shapes to test smoothness.")
'''
    
    return freecad_config

def create_freecad_macro():
    """Create a FreeCAD macro file for smooth curves configuration"""
    
    # Create macro directory if it doesn't exist
    macro_dir = os.path.expanduser("~/.local/share/FreeCAD/Macro")
    os.makedirs(macro_dir, exist_ok=True)
    
    macro_file = os.path.join(macro_dir, "SmoothCurvesConfig.FCMacro")
    
    with open(macro_file, 'w') as f:
        f.write(configure_freecad_smooth_curves())
    
    print(f"Created FreeCAD macro: {macro_file}")
    return macro_file

def print_manual_instructions():
    """Print manual configuration instructions"""
    
    print("""
=== Manual FreeCAD Configuration for Smooth Curves ===

1. Open FreeCAD (or FreeCAD Daily)
2. Go to Edit → Preferences
3. Navigate to Display → 3D View

4. TESSELLATION SETTINGS (Most Important):
   - Mesh deviation: 0.01 (default is usually 0.2)
   - Angular deflection: 5.0° (default is usually 28.5°)
   - Maximum deviation: 0.005
   - Maximum angular deflection: 2.0°

5. ANTI-ALIASING:
   - ✓ Enable anti-aliasing
   - ✓ Enable smooth lines
   - ✓ Enable transparency anti-aliasing

6. PERFORMANCE:
   - ✓ Use VBO (Vertex Buffer Objects)
   - ✓ Use OpenGL acceleration

7. Click OK and restart FreeCAD

=== What These Settings Do ===

• Mesh Deviation: Controls how closely the tessellated mesh follows curved surfaces
  - Lower values = smoother curves but more GPU/CPU load
  - 0.01 is very smooth, 0.2 is quite jagged

• Angular Deflection: Maximum angle between adjacent tessellation segments
  - Lower values = smoother curves
  - 5° is very smooth, 28.5° is quite angular

• Anti-aliasing: Smooths jagged edges in the display
• VBO: Uses GPU memory for better performance with smooth meshes

=== GPU vs FreeCAD Issue ===
This is definitely a FreeCAD tessellation issue, not GPU configuration.
The NVIDIA GPU will help render the smooth tessellation faster once configured.
""")

if __name__ == "__main__":
    print("FreeCAD Smooth Curves Configuration")
    print("=" * 50)
    
    # Create macro file
    macro_file = create_freecad_macro()
    
    print(f"""
=== Three Ways to Apply These Settings ===

1. AUTOMATIC (Recommended):
   - Open FreeCAD Daily
   - Go to Macro → Macros...
   - Select 'SmoothCurvesConfig.FCMacro'
   - Click Execute

2. COMMAND LINE:
   freecad-daily --run-test "{macro_file}"

3. MANUAL:
   Follow the manual instructions below
""")
    
    print_manual_instructions()
